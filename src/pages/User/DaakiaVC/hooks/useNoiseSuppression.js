import { useEffect, useRef, useState } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

export const useNoiseSuppression = (room) => {
  const { noiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessorRef = useRef(null);
  const originalTrackRef = useRef(null);
  const processedTrackRef = useRef(null);
  const currentDeviceIdRef = useRef(null);

  const [isProcessing, setIsProcessing] = useState(false);
  const [isNoiseSuppressionActive, setIsNoiseSuppressionActive] = useState(false);

  const currentOperationRef = useRef(null);
  const retryCountRef = useRef(0);
  const maxRetries = 10;

  useEffect(() => {
    const applyNoiseSuppression = async () => {
      if (!room || room.state !== "connected") return;
      if (isProcessing) {
        return;
      }

      const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      if (!audioTrackPublication || !audioTrackPublication.track) {
        retryCountRef.current = 0;
        return;
      }

      const localAudioTrack = audioTrackPublication.track;
      
      // Wait for track to be ready if it's still being set up
              if (!localAudioTrack.mediaStreamTrack) {
          if (retryCountRef.current < maxRetries) {
            retryCountRef.current += 1;
            console.log(`Track not ready yet, will retry... (${retryCountRef.current}/${maxRetries})`);
          setTimeout(() => {
            if (room && room.state === "connected") {
              applyNoiseSuppression();
            }
          }, 500);
          return;
        } else {
          console.warn('Max retries reached, track still not ready. Giving up.');
          retryCountRef.current = 0;
          return;
        }
      }

      // Reset retry count when track is ready
      retryCountRef.current = 0;
      
      const { mediaStreamTrack } = localAudioTrack;
      
      // Check if the track is ended, but allow some time for recovery
      if (mediaStreamTrack.readyState === 'ended') {
        console.log('Track is ended, waiting for new track...');
                 // For ended tracks, retry with a longer delay to allow track recovery
         if (retryCountRef.current < maxRetries) {
           retryCountRef.current += 1;
           setTimeout(() => {
            if (room && room.state === "connected") {
              applyNoiseSuppression();
            }
          }, 1000); // Longer delay for ended tracks
          return;
        } else {
          console.warn('Max retries reached for ended track, giving up.');
          retryCountRef.current = 0;
          return;
        }
      }
      
      // Log track state for debugging
      console.log('Track state:', {
        readyState: mediaStreamTrack.readyState,
        enabled: mediaStreamTrack.enabled,
        muted: mediaStreamTrack.muted,
        deviceId: mediaStreamTrack.getSettings().deviceId
      });
      
      const currentDeviceId = mediaStreamTrack.getSettings().deviceId;

      // Check if device has changed - if so, reset noise suppression state
      const hasDeviceChanged = currentDeviceIdRef.current && 
                               currentDeviceIdRef.current !== currentDeviceId && 
                               currentDeviceId;

      if (hasDeviceChanged) {
        console.log('Device changed, resetting noise suppression state');
        retryCountRef.current = 0;
        
        // Clean up previous device's tracks and processor asynchronously
        // Don't await this to avoid blocking the new device setup
        const cleanupPrevious = async () => {
          if (noiseProcessorRef.current) {
            try {
              await noiseProcessorRef.current.stopProcessing();
              noiseProcessorRef.current = null;
            } catch (error) {
              console.error('Failed to stop noise processor during device change:', error);
            }
          }
          
          if (processedTrackRef.current) {
            try {
              processedTrackRef.current.stop();
            } catch (error) {
              // Track might already be stopped, ignore error
            }
            processedTrackRef.current = null;
          }
        };

        // Start cleanup but don't wait for it
        cleanupPrevious();
        
        // Reset state immediately
        originalTrackRef.current = null;
        setIsNoiseSuppressionActive(false);
        
        // Update current device ID
        currentDeviceIdRef.current = currentDeviceId;
        
        // Schedule a new attempt after a delay to let the device settle
        setTimeout(() => {
          if (room && room.state === "connected") {
            applyNoiseSuppression();
          }
        }, 1500); // Give device change time to settle
        
        return;
      }

      // Update current device ID (do this after the change check)
      currentDeviceIdRef.current = currentDeviceId;

      // Now check if we need to change noise suppression state
      if (noiseSuppressionEnabled === isNoiseSuppressionActive) {
        return;
      }

      // Cancel any existing operation
      if (currentOperationRef.current) {
        currentOperationRef.current.cancelled = true;
      }

      // Create new operation tracker
      const currentOperation = { cancelled: false };
      currentOperationRef.current = currentOperation;

      try {
        setIsProcessing(true);

        // Check if operation was cancelled before proceeding
        if (currentOperation.cancelled) {
          return;
        }

        if (noiseSuppressionEnabled && !isNoiseSuppressionActive) {
          // Check cancellation before starting expensive operations
          if (currentOperation.cancelled) {
            return;
          }

          // Validate track state before processing
          if (!mediaStreamTrack || mediaStreamTrack.readyState === 'ended') {
            console.warn('Cannot apply noise suppression: track is ended or invalid');
            return;
          }

          // Store device info to recreate a fresh track when needed
          const currentSettings = mediaStreamTrack.getSettings();
          const originalDeviceInfo = {
            deviceId: currentSettings.deviceId || 'default',
            constraints: {
              audio: {
                deviceId: currentSettings.deviceId || 'default',
                echoCancellation: true,
                noiseSuppression: false,
                autoGainControl: true
              }
            }
          };

          noiseProcessorRef.current = new NoiseSuppressionProcessor();

          // Check cancellation before async processing
          if (currentOperation.cancelled) {
            return;
          }

          // Double-check track state right before processing
          if (mediaStreamTrack.readyState === 'ended') {
            console.warn('Track became ended before processing, skipping noise suppression');
            return;
          }

          const processedTrack = await noiseProcessorRef.current.startProcessing(
            mediaStreamTrack
          );

          // Check cancellation after async processing
          if (currentOperation.cancelled) {
            // Clean up processed track if operation was cancelled
            if (processedTrack) {
              processedTrack.stop();
            }
            return;
          }

          if (processedTrack) {
            // Store the processed track reference and original device info
            processedTrackRef.current = processedTrack;
            originalTrackRef.current = originalDeviceInfo;

            // Replace the original track with the processed one
            await localAudioTrack.replaceTrack(processedTrack, true);

            // Final cancellation check before setting state
            if (currentOperation.cancelled) {
              return;
            }

            // Mark noise suppression as active
            setIsNoiseSuppressionActive(true);
          }
        } else if (!noiseSuppressionEnabled && isNoiseSuppressionActive) {
          // Check cancellation before starting disable operations
          if (currentOperation.cancelled) {
            return;
          }

          try {
            // Stop the noise processor first
            if (noiseProcessorRef.current) {
              await noiseProcessorRef.current.stopProcessing();
              noiseProcessorRef.current = null;
            }
            processedTrackRef.current = null;

            // Check cancellation after stopping processor
            if (currentOperation.cancelled) {
              return;
            }

            // Create a fresh audio track using stored device info or current settings
            let constraints;
            if (originalTrackRef.current && originalTrackRef.current.constraints) {
              constraints = originalTrackRef.current.constraints;
            } else {
              const currentSettings = localAudioTrack.mediaStreamTrack.getSettings();
              constraints = {
                audio: {
                  deviceId: currentSettings.deviceId || 'default',
                  echoCancellation: true,
                  noiseSuppression: false,
                  autoGainControl: true
                }
              };
            }

            const stream = await navigator.mediaDevices.getUserMedia(constraints);

            // Check cancellation after getUserMedia
            if (currentOperation.cancelled) {
              // Clean up the new stream if operation was cancelled
              stream.getTracks().forEach(track => track.stop());
              return;
            }

            const newAudioTrack = stream.getAudioTracks()[0];

            if (newAudioTrack) {
              await localAudioTrack.replaceTrack(newAudioTrack, true);
            }

            // Final cancellation check before cleanup and state update
            if (currentOperation.cancelled) {
              // Clean up the new track if operation was cancelled after replacement
              if (newAudioTrack) {
                newAudioTrack.stop();
              }
              return;
            }

            // Clear references
            originalTrackRef.current = null;

            // Mark noise suppression as inactive
            setIsNoiseSuppressionActive(false);
          } catch (restoreError) {
            console.error('Failed to restore original audio track:', restoreError);
            // Still set state to inactive even if restore failed
            setIsNoiseSuppressionActive(false);
          }
        }
      } catch (error) {
        console.error('Noise suppression operation failed:', error);
        // Reset processing state on any error
        setIsNoiseSuppressionActive(false);

        // Clean up any tracks that might have been created but not properly handled
        originalTrackRef.current = null;
        
        if (processedTrackRef.current) {
          processedTrackRef.current.stop();
          processedTrackRef.current = null;
        }
      } finally {
        setIsProcessing(false);
        // Clear the current operation reference
        if (currentOperationRef.current === currentOperation) {
          currentOperationRef.current = null;
        }
      }
    };

    // Only run when room is connected and we need to change state
    if (room && room.state === "connected") {
      // Increased delay to ensure audio track is fully initialized after device changes
      setTimeout(applyNoiseSuppression, 1000);
    }

    // No cleanup function - let the state persist
  }, [room, room?.state, noiseSuppressionEnabled, isProcessing, isNoiseSuppressionActive]);

  // Separate cleanup effect for when component unmounts
  useEffect(() => {
    return () => {
      // Cancel any ongoing operations
      if (currentOperationRef.current) {
        currentOperationRef.current.cancelled = true;
        currentOperationRef.current = null;
      }

      if (noiseProcessorRef.current) {
        try {
          noiseProcessorRef.current.stopProcessing();
          noiseProcessorRef.current = null;
        } catch (error) {
          console.error('Failed to stop noise processor during cleanup:', error);
        }
      }

      // Clean up all track references
      originalTrackRef.current = null;

      if (processedTrackRef.current) {
        // Stop processed track if it's still active
        try {
          processedTrackRef.current.stop();
        } catch (error) {
          // Track might already be stopped, ignore error
        }
        processedTrackRef.current = null;
      }

      currentDeviceIdRef.current = null;
      retryCountRef.current = 0;
      setIsProcessing(false);
      setIsNoiseSuppressionActive(false);
    };
  }, []);

  // Return hook state and methods
  return {
    isNoiseSuppressionActive,
    isProcessing,
  };
};